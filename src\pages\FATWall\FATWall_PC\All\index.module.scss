.container {
  width: 100%;
  height: 100%;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  user-select: none;
}

.header {
  display: flex;
  height: 60px;
  justify-content: space-between;
  align-items: center;
}

.content {
  width: 100%;
  height: calc(100% - 60px);
  padding: 0 28px;
  position: relative;
  overflow-y: auto;
  scrollbar-width: none;
}

.tabs_item {
  min-width: 65px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: MiSans W;
  font-weight: 400;
  font-size: 15.11px;
  line-height: 100%;
  letter-spacing: 0px;
  border-radius: 6px;
  cursor: pointer;
  color: var(--subtitle-text-color);
}

.tabs_item.selected {
  padding: 5px 10px;
  font-weight: 700;
  color: #fff;
  background-color: var(--tab-btn-color);
}

.filter_films_container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.film_card_container {
  position: relative;
  flex: 0 0 auto; // 禁止伸缩
  display: flex;
  justify-content: center;
  transition: transform 0.2s;
  padding: 6px 6px;
  border-radius: 12px;

  &:active {
    transform: scale(0.98); // 点击反馈
  }
  &:hover {
    background-color: var(--fat-card-hover-bg);
  }
}

.modal_button {
  width: 100%;
  height: 50px;
  background-color: var(--cancel-btn-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
  border-radius: 16px;
  font-family: MiSans;
  font-weight: 500;
  font-size: 17px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
  color: var(--text-color);
  cursor: pointer;

  &:hover {
    background-color: #fff;
    color: #4096ff;
  }
}

.films_card_error_container {
  width: 100%;
}
